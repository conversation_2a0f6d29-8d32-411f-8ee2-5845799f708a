"""
File Utilities
==============

Utility functions for file operations.
"""

import os
import shutil
from pathlib import Path
from datetime import datetime
from typing import Dict, Optional, List
import json
import logging

from models.client_profile import ClientProfile
from config.export_config import ExportConfig


class FileUtils:
    """Utility class for file operations."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.export_config = ExportConfig()
    
    def create_timestamped_output_directory(self, client_profile: ClientProfile) -> Dict[str, Path]:
        """Create timestamped output directory structure."""
        
        # Get base output directory
        base_dir = Path("outputs")
        
        # Create timestamp
        timestamp = datetime.now()
        date_str = timestamp.strftime("%Y-%m-%d")
        
        # Create client-specific directory name
        client_name = client_profile.get_clean_company_name()
        
        # Create directory structure
        if self.export_config.should_create_date_folders():
            if self.export_config.should_create_client_folders():
                output_dir = base_dir / date_str / client_name
            else:
                output_dir = base_dir / date_str
        else:
            if self.export_config.should_create_client_folders():
                output_dir = base_dir / client_name
            else:
                output_dir = base_dir
        
        # Create subdirectories
        directories = {
            'main_dir': output_dir,
            'data_dir': output_dir / "data",
            'charts_dir': output_dir / "charts",
            'reports_dir': output_dir / "reports"
        }
        
        # Create all directories
        for dir_path in directories.values():
            dir_path.mkdir(parents=True, exist_ok=True)
        
        self.logger.info(f"Created output directory structure: {output_dir}")
        return directories
    
    def generate_filename(self, file_type: str, client_profile: ClientProfile, 
                         include_timestamp: bool = True) -> str:
        """Generate filename based on configuration."""
        
        # Get base template
        template = self.export_config.get_filename_template(file_type)
        
        # Prepare replacement values
        replacements = {}
        
        if include_timestamp:
            timestamp = datetime.now().strftime(self.export_config.get_timestamp_format())
            replacements['timestamp'] = timestamp
        
        if self.export_config.include_client_name:
            client_name = client_profile.get_clean_company_name()
            replacements['client_name'] = client_name
        
        # Replace placeholders
        filename = template
        for key, value in replacements.items():
            filename = filename.replace(f'{{{key}}}', value)
        
        return filename
    
    def save_json_data(self, data: Dict, filepath: Path) -> bool:
        """Save data as JSON file."""
        try:
            filepath.parent.mkdir(parents=True, exist_ok=True)
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False, default=str)
            
            self.logger.info(f"JSON data saved to: {filepath}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error saving JSON data: {str(e)}")
            return False
    
    def load_json_data(self, filepath: Path) -> Optional[Dict]:
        """Load data from JSON file."""
        try:
            if not filepath.exists():
                return None
            
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            self.logger.info(f"JSON data loaded from: {filepath}")
            return data
            
        except Exception as e:
            self.logger.error(f"Error loading JSON data: {str(e)}")
            return None
    
    def copy_file(self, source: Path, destination: Path) -> bool:
        """Copy file from source to destination."""
        try:
            destination.parent.mkdir(parents=True, exist_ok=True)
            shutil.copy2(source, destination)
            
            self.logger.info(f"File copied from {source} to {destination}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error copying file: {str(e)}")
            return False
    
    def move_file(self, source: Path, destination: Path) -> bool:
        """Move file from source to destination."""
        try:
            destination.parent.mkdir(parents=True, exist_ok=True)
            shutil.move(str(source), str(destination))
            
            self.logger.info(f"File moved from {source} to {destination}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error moving file: {str(e)}")
            return False
    
    def delete_file(self, filepath: Path) -> bool:
        """Delete file."""
        try:
            if filepath.exists():
                filepath.unlink()
                self.logger.info(f"File deleted: {filepath}")
                return True
            return False
            
        except Exception as e:
            self.logger.error(f"Error deleting file: {str(e)}")
            return False
    
    def get_file_size(self, filepath: Path) -> int:
        """Get file size in bytes."""
        try:
            return filepath.stat().st_size if filepath.exists() else 0
        except Exception:
            return 0
    
    def format_file_size(self, size_bytes: int) -> str:
        """Format file size in human readable format."""
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        
        return f"{size_bytes:.1f} {size_names[i]}"
    
    def list_files_in_directory(self, directory: Path, 
                               extensions: Optional[List[str]] = None) -> List[Path]:
        """List files in directory with optional extension filter."""
        try:
            if not directory.exists():
                return []
            
            files = []
            for file_path in directory.iterdir():
                if file_path.is_file():
                    if extensions is None or file_path.suffix.lower() in extensions:
                        files.append(file_path)
            
            return sorted(files)
            
        except Exception as e:
            self.logger.error(f"Error listing files: {str(e)}")
            return []
    
    def create_backup(self, filepath: Path) -> Optional[Path]:
        """Create backup of file."""
        try:
            if not filepath.exists():
                return None
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = filepath.with_suffix(f".{timestamp}.backup{filepath.suffix}")
            
            shutil.copy2(filepath, backup_path)
            self.logger.info(f"Backup created: {backup_path}")
            return backup_path
            
        except Exception as e:
            self.logger.error(f"Error creating backup: {str(e)}")
            return None
    
    def cleanup_old_files(self, directory: Path, days_old: int = 30) -> int:
        """Clean up files older than specified days."""
        try:
            if not directory.exists():
                return 0
            
            cutoff_time = datetime.now().timestamp() - (days_old * 24 * 60 * 60)
            deleted_count = 0
            
            for file_path in directory.rglob("*"):
                if file_path.is_file():
                    if file_path.stat().st_mtime < cutoff_time:
                        file_path.unlink()
                        deleted_count += 1
            
            self.logger.info(f"Cleaned up {deleted_count} old files from {directory}")
            return deleted_count
            
        except Exception as e:
            self.logger.error(f"Error cleaning up files: {str(e)}")
            return 0
    
    def ensure_directory_exists(self, directory: Path) -> bool:
        """Ensure directory exists, create if necessary."""
        try:
            directory.mkdir(parents=True, exist_ok=True)
            return True
        except Exception as e:
            self.logger.error(f"Error creating directory {directory}: {str(e)}")
            return False
    
    def get_safe_filename(self, filename: str) -> str:
        """Get filesystem-safe filename."""
        # Remove or replace unsafe characters
        unsafe_chars = '<>:"/\\|?*'
        safe_filename = filename
        
        for char in unsafe_chars:
            safe_filename = safe_filename.replace(char, '_')
        
        # Remove multiple consecutive underscores
        while '__' in safe_filename:
            safe_filename = safe_filename.replace('__', '_')
        
        # Remove leading/trailing underscores and spaces
        safe_filename = safe_filename.strip('_ ')
        
        return safe_filename
